import psycopg2

# Database connection settings (change as needed)
DB_NAME = "formulas_db"
DB_USER = "postgres"
DB_PASSWORD = "123456"
DB_HOST = "localhost"
DB_PORT = "5432"

# SQL schema creation
create_tables_sql = """
DROP TABLE IF EXISTS formula_components, formulas, symbols, syntax_rules CASCADE;

CREATE TABLE symbols (
    id SERIAL PRIMARY KEY,
    symbol CHAR(1) UNIQUE NOT NULL,
    meaning TEXT NOT NULL
);

CREATE TABLE formulas (
    id SERIAL PRIMARY KEY,
    expression TEXT NOT NULL,
    description TEXT
);

CREATE TABLE formula_components (
    id SERIAL PRIMARY KEY,
    formula_id INTEGER REFERENCES formulas(id) ON DELETE CASCADE,
    component TEXT NOT NULL,
    position INTEGER NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('symbol', 'operator', 'grouping'))
);

CREATE TABLE syntax_rules (
    id SERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    meaning TEXT NOT NULL
);
"""

symbol_meanings = [
    ('e', 'Essence'), ('i', 'Identity'), ('f', 'Function'), ('d', 'Description'),
    ('c', 'Success'), ('v', 'Vision'), ('o', 'Objective'), ('r', 'Reason'),
    ('p', 'Purpose'), ('s', 'Solution'), ('t', 'State'), ('b', 'Possibility'),
    ('n', 'Science'), ('m', 'Method')
]

def initialize_database():
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME, user=DB_USER, password=DB_PASSWORD,
            host=DB_HOST, port=DB_PORT
        )
        cur = conn.cursor()

        print("Creating tables...")
        cur.execute(create_tables_sql)

        print("Inserting symbols...")
        for symbol, meaning in symbol_meanings:
            cur.execute("INSERT INTO symbols (symbol, meaning) VALUES (%s, %s)", (symbol, meaning))

        conn.commit()
        print("Database initialized and data inserted successfully.")
        cur.close()
        conn.close()

    except Exception as e:
        print("Error:", e)

if __name__ == "__main__":
    initialize_database()
