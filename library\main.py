import psycopg2

# Database connection settings (change as needed)
DB_NAME = "formulas_db"
DB_USER = "postgres"
DB_PASSWORD = "123456"
DB_HOST = "localhost"
DB_PORT = "5432"

# SQL schema creation
create_tables_sql = """
DROP TABLE IF EXISTS formula_components, formulas, symbols, syntax_rules CASCADE;

CREATE TABLE symbols (
    id SERIAL PRIMARY KEY,
    symbol CHAR(1) UNIQUE NOT NULL,
    meaning TEXT NOT NULL
);

CREATE TABLE formulas (
    id SERIAL PRIMARY KEY,
    expression TEXT NOT NULL,
    description TEXT
);

CREATE TABLE formula_components (
    id SERIAL PRIMARY KEY,
    formula_id INTEGER REFERENCES formulas(id) ON DELETE CASCADE,
    component TEXT NOT NULL,
    position INTEGER NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('symbol', 'operator', 'grouping'))
);

CREATE TABLE syntax_rules (
    id SERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    meaning TEXT NOT NULL
);
"""

symbol_meanings = [
    ('e', 'Essence'), ('i', 'Identity'), ('f', 'Function'), ('d', 'Description'),
    ('c', 'Success'), ('v', 'Vision'), ('o', 'Objective'), ('r', 'Reason'),
    ('p', 'Purpose'), ('s', 'Solution'), ('t', 'State'), ('b', 'Possibility'),
    ('n', 'Science'), ('m', 'Method')
]

formulas = [
    ("e=|>t(i)+i(t)", "Essence equals input of State of Identity plus Identity of State"),
    ("i=|>p(f)+f(p)", "Identity equals input of Purpose of Function plus Function of Purpose"),
    ("f=|>m(o)+o(m)", "Function equals input of Method of Objective plus Objective of Method"),
    ("d=|>e(i)+i(e)", "Description equals input of Essence of Identity plus Identity of Essence"),
    ("c=|>b(s)+s(b)", "Success equals input of Possibility of Solution plus Solution of Possibility"),
]

syntax_rules = [
    ("=", "Equals / Definition"),
    ("+","Addition / Combination"),
    ("(", "Start of parameter group"),
    (")", "End of parameter group"),
    ("[", "Start of selection/optional group"),
    ("]", "End of selection/optional group"),
    ("|>", "Function input / Flow from left to right")
]

def insert_formulas_and_syntax():
    try:
        conn = psycopg2.connect(
            dbname="formulas_db", user="postgres", password="yourpassword",
            host="localhost", port="5432"
        )
        cur = conn.cursor()

        # Insert syntax rules
        for sym, meaning in syntax_rules:
            cur.execute("INSERT INTO syntax_rules (symbol, meaning) VALUES (%s, %s)", (sym, meaning))

        # Insert formulas and their components
        for expression, description in formulas:
            cur.execute("INSERT INTO formulas (expression, description) VALUES (%s, %s) RETURNING id",
                        (expression, description))
            result = cur.fetchone()
            if result is None:
                raise Exception("Failed to insert formula, no ID returned")
            formula_id = result[0]

            # Parse expression into components
            components = []
            buffer = ''
            operators = {'=', '+', '(', ')', '[', ']', '|>'}

            i = 0
            while i < len(expression):
                if expression[i:i+2] == '|>':
                    components.append(('|>', 'operator'))
                    i += 2
                elif expression[i] in operators:
                    if expression[i] in '()[]':
                        components.append((expression[i], 'grouping'))
                    else:
                        components.append((expression[i], 'operator'))
                    i += 1
                else:
                    components.append((expression[i], 'symbol'))
                    i += 1

            for idx, (comp, comp_type) in enumerate(components):
                cur.execute("""
                    INSERT INTO formula_components (formula_id, component, position, type)
                    VALUES (%s, %s, %s, %s)
                """, (formula_id, comp, idx, comp_type))

        conn.commit()
        cur.close()
        conn.close()
        "Formulas and syntax rules inserted successfully."
    except Exception as e:
        str(e)

def initialize_database():
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME, user=DB_USER, password=DB_PASSWORD,
            host=DB_HOST, port=DB_PORT
        )
        cur = conn.cursor()

        print("Creating tables...")
        cur.execute(create_tables_sql)

        print("Inserting symbols...")
        for symbol, meaning in symbol_meanings:
            cur.execute("INSERT INTO symbols (symbol, meaning) VALUES (%s, %s)", (symbol, meaning))

        conn.commit()
        print("Database initialized and data inserted successfully.")
        cur.close()
        conn.close()

    except Exception as e:
        print("Error:", e)

if __name__ == "__main__":
    initialize_database()
