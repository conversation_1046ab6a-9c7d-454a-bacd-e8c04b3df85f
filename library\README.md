# Aseity Library

A symbolic formula management system for storing and organizing conceptual formulas with their symbolic representations and meanings.

## Database Schema

The system uses four main tables:

### Symbols
- Stores individual symbols (single characters) and their meanings
- Pre-populated with 14 conceptual symbols

### Formulas
- Stores formula expressions and descriptions
- Links to components through foreign key relationships

### Formula Components
- Breaks down formulas into individual components
- Tracks position and type (symbol, operator, grouping)

### Syntax Rules
- Defines rules for symbol usage and meaning

## Symbol Meanings

The system comes pre-configured with the following symbolic meanings:

| Symbol | Meaning |
|--------|---------|
| e | Essence |
| i | Identity |
| f | Function |
| d | Description |
| c | Success |
| v | Vision |
| o | Objective |
| r | Reason |
| p | Purpose |
| s | Solution |
| t | State |
| b | Possibility |
| n | Science |
| m | Method |

## Prerequisites

- Python 3.x
- PostgreSQL database server
- psycopg2 library

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aseity
   ```

2. **Install dependencies**
   ```bash
   pip install psycopg2-binary
   ```

3. **Set up PostgreSQL**
   - Install PostgreSQL on your system
   - Create a database named `formulas_db`
   - Ensure PostgreSQL is running on localhost:5432

4. **Configure database connection**
   
   Edit the database connection settings in `library/main.py`:
   ```python
   DB_NAME = "formulas_db"
   DB_USER = "postgres"
   DB_PASSWORD = "your_password"
   DB_HOST = "localhost"
   DB_PORT = "5432"
   ```

## Usage

### Initialize the Database

Run the main script to create tables and populate initial data:

```bash
cd library
python main.py
```

This will:
- Drop existing tables (if any)
- Create the database schema
- Insert the predefined symbols and their meanings

### Database Operations

After initialization, you can connect to the database and perform operations:

```python
import psycopg2

# Connect to database
conn = psycopg2.connect(
    dbname="formulas_db",
    user="postgres", 
    password="your_password",
    host="localhost",
    port="5432"
)

# Your database operations here
```

## Development

### Adding New Symbols

To add new symbols, modify the `symbol_meanings` list in `library/main.py`:

```python
symbol_meanings = [
    # Existing symbols...
    ('x', 'New Concept'),  # Add new symbol here
]
```

### Creating Formulas

Formulas can be added to the database by inserting into the `formulas` table and breaking them down into components in the `formula_components` table.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request